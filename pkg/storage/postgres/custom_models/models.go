package custom_models

type Location struct {
	Latitude  float64 `json:"latitude" example:"-5.9015168"`
	Longitude float64 `json:"longitude" example:"-35.2485376"`
}

type AddressParams struct {
	Name         string   `json:"name" example:"Home"`
	IsDefault    bool     `json:"is_default,omitempty" example:"true"`
	ExternalID   string   `json:"external_id,omitempty" example:"123456"`
	Street       string   `json:"street" example:"Rua das Flores"`
	Number       string   `json:"number" example:"123"`
	Neighborhood string   `json:"neighborhood" example:"Downtown"`
	ZipCode      string   `json:"zip_code" example:"12345678"`
	City         string   `json:"city" example:"São Paulo"`
	State        string   `json:"state" example:"SP"`
	Location     Location `json:"location"`
	Complement   string   `json:"complement" example:"Apto 101"`
}

type Product struct {
	ID          int32      `json:"id,omitempty" example:"1"`
	Name        string     `json:"name" example:"água De Coco Taeq"`
	Description string     `json:"description,omitempty" example:"água de coco integral taeq 200ml - SEM GLÚTEN - 100% NATURAL"`
	Ean         string     `json:"ean" example:"7895000292035"`
	Image       string     `json:"image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	Price       int32      `json:"price,omitempty" example:"1000"`
	Stock       int32      `json:"stock,omitempty" example:"10"`
	Quantity    int32      `json:"quantity,omitempty" example:"1"`
	Discount    int32      `json:"discount,omitempty" example:"10" `
	IsActive    bool       `json:"is_active" example:"true"`
	Is18Plus    bool       `json:"is_18_plus" example:"false"`
	Brand       string     `json:"brand" example:"Taeq"`
	ExternalID  string     `json:"external_id,omitempty" example:"123456"`
	Categories  []Category `json:"categories,omitempty"`
}

type ProductList struct {
	ExternalID string `json:"external_id" example:"01JSFHYVFQEF8R335QNFDDJ6ZZ" `
	Quantity   int32  `json:"quantity" example:"1" `
}

type Category struct {
	Name       string `json:"name" form:"category_name" example:"Bebidas"`
	Image      string `json:"image" form:"category_image" example:"https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg"`
	ExternalID string `json:"external_id" form:"category_external_id" example:"123456"`
}

type CompanyProduct struct {
	ProductExternalID string `json:"product_external_id" example:"01JSFHYVFQEF8R335QNFDDJ6ZZ" `
	Price             int32  `json:"price" example:"1000" `
	Stock             int32  `json:"stock" example:"10" `
	Discount          int32  `json:"discount" example:"10" `
	Reference         string `json:"reference" example:"kg" `
}
