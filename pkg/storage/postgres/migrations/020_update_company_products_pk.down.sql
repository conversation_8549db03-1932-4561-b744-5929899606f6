-- Revert company_products primary key change
-- 1) Drop the new unique index
DROP INDEX IF EXISTS idx_company_products_unique;

-- 2) Drop the composite PK including reference
ALTER TABLE company_products DROP CONSTRAINT IF EXISTS company_products_pkey;

-- 3) Restore original primary key (company_id, product_id)
ALTER TABLE company_products ADD CONSTRAINT company_products_pkey PRIMARY KEY (company_id, product_id);

-- 4) Recreate original unique index
CREATE UNIQUE INDEX IF NOT EXISTS idx_company_products_unique ON company_products (company_id, product_id);

-- 5) Allow reference to be NULL again (optional, mirrors previous state)
ALTER TABLE company_products ALTER COLUMN reference DROP NOT NULL;

