-- Create subscriptions table first since other tables reference it
CREATE TABLE IF NOT EXISTS "subscriptions" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "price" integer NOT NULL,
  "discount" integer NOT NULL DEFAULT 0,
  "picture" text NOT NULL,
  "is_active" boolean NOT NULL DEFAULT true,
  "external_id" text NOT NULL UNIQUE,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

-- Create users table with subscription_id foreign key
CREATE TABLE IF NOT EXISTS "users" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "email" text NOT NULL UNIQUE,
  "login_code" text NOT NULL DEFAULT '',
  "cpf" text NOT NULL UNIQUE,
  "phone_numbers" text [],
  "cashback_value" integer NOT NULL DEFAULT 0,
  "subscription_id" integer REFERENCES subscriptions(id) DEFAULT 1,
  "is_active" boolean NOT NULL DEFAULT true,
  "is_deleted" boolean NOT NULL DEFAULT false,
  "external_id" text NOT NULL UNIQUE,
  "fcm_token" VARCHAR(255),
  "fcm_token_updated_at" TIMESTAMP,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

CREATE TABLE IF NOT EXISTS "user_addresses" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "name" TEXT NOT NULL, -- Name of the address (e.g., Home, Work)
  "street" TEXT NOT NULL,
  "number" TEXT NOT NULL,
  "complement" TEXT,
  "neighborhood" TEXT NOT NULL,
  "city" TEXT NOT NULL,
  "state" TEXT NOT NULL,
  "zip_code" TEXT NOT NULL,
  "latitude" DOUBLE PRECISION,
  "longitude" DOUBLE PRECISION,
  "location" GEOMETRY(POINT, 4326) NOT NULL, -- PostGIS point for spatial queries
  "is_default" BOOLEAN NOT NULL DEFAULT false, -- Indicates if this is the default address
  "external_id" TEXT NOT NULL UNIQUE, -- Unique identifier for the address
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now()),
  UNIQUE(user_id, external_id)
);

-- Create roles table
CREATE TABLE IF NOT EXISTS "roles" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "description" TEXT NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMP NOT NULL DEFAULT (now())
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS "user_roles" (
  "user_id" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "role_id" INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  PRIMARY KEY (user_id, role_id)
);

CREATE TABLE IF NOT EXISTS "categories" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "image" text NOT NULL,
  "external_id" text NOT NULL UNIQUE,
  "is_active" boolean NOT NULL DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

-- Create products table
CREATE TABLE IF NOT EXISTS "products" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "ean" text NOT NULL UNIQUE,
  "description" text ,
  "image" text ,
  "brand" text ,
  "is_reviewed" boolean NOT NULL DEFAULT false,
  "is_active" boolean NOT NULL DEFAULT true,
  "is_18_plus" boolean NOT NULL DEFAULT false,
  "external_id" text NOT NULL UNIQUE,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

-- Tabela de junção N:N entre produtos e categorias
CREATE TABLE IF NOT EXISTS "products_categories" (
  "product_id" INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  "category_id" INTEGER NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY ("product_id", "category_id")
);

CREATE TABLE IF NOT EXISTS "companies" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "cnpj" text NOT NULL UNIQUE,
  "bio" text NOT NULL,
  "picture" text NOT NULL,
  "phone_numbers" text[] NOT NULL,
  "pix_key" text NOT NULL UNIQUE,
  "commission_rate" integer NOT NULL DEFAULT 1500,
  "cashback_rate" integer NOT NULL DEFAULT 200,
  "subscription_id" integer REFERENCES subscriptions(id),
  "shipping_fee" integer NOT NULL DEFAULT 0,
  "rating" float NOT NULL DEFAULT 5,
  "owner_id" integer REFERENCES users(id),
  "affiliate_balance" integer NOT NULL DEFAULT 0,
  "external_id" text NOT NULL UNIQUE,
  "is_active" boolean NOT NULL DEFAULT false,
  "delivery_modes" text[] NOT NULL DEFAULT ARRAY['pickup'],
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now()),
  CONSTRAINT delivery_modes_check CHECK (
    delivery_modes <@ ARRAY['delivery', 'pickup']
    AND cardinality(delivery_modes) > 0
  )
);

CREATE TABLE IF NOT EXISTS "company_addresses" (
  "id" SERIAL PRIMARY KEY,
  "company_id" INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  "name" TEXT NOT NULL, -- Name of the address (e.g., Home, Work)
  "street" TEXT NOT NULL,
  "number" TEXT NOT NULL,
  "complement" TEXT,
  "neighborhood" TEXT NOT NULL,
  "city" TEXT NOT NULL,
  "state" TEXT NOT NULL,
  "zip_code" TEXT NOT NULL,
  "latitude" DOUBLE PRECISION,
  "longitude" DOUBLE PRECISION,
  "location" GEOMETRY(POINT, 4326) NOT NULL, -- PostGIS point for spatial queries
  "is_default" BOOLEAN NOT NULL DEFAULT false, -- Indicates if this is the default address
  "external_id" TEXT NOT NULL UNIQUE, -- Unique identifier for the address
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

CREATE TABLE IF NOT EXISTS "company_products" (
  "company_id" INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  "product_id" INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  "price" NUMERIC NOT NULL,
  "discount" NUMERIC NOT NULL DEFAULT 0,
  "stock" INTEGER NOT NULL DEFAULT 0,
  "reference" TEXT,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  PRIMARY KEY (company_id, product_id, reference)
);

-- Create company_withdrawals table for tracking partner withdrawal requests
CREATE TABLE IF NOT EXISTS "company_withdrawals" (
    "id" BIGSERIAL PRIMARY KEY,
    "company_id" BIGINT NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    "amount" INTEGER NOT NULL CHECK (amount > 0), -- Amount in cents
    "correlation_id" VARCHAR(255), -- Woovi correlation ID for tracking
    "destination_pix_key" VARCHAR(255), -- Destination account info from Woovi
    "comment" TEXT, -- Additional notes or comments
    "end_to_end_id" VARCHAR(255), -- PIX end-to-end ID when available
    "woovi_response" JSONB, -- Store full Woovi API response for audit
    "status" VARCHAR(20) NOT NULL DEFAULT 'CREATED' CHECK (status IN ('CREATED', 'CONFIRMED', 'FAILED')), -- Withdrawal status
    "finished_at" TIMESTAMP, -- When the withdrawal was completed or failed
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS "campaigns" (
  "id" SERIAL PRIMARY KEY,
  "name" text NOT NULL,
  "duration" integer NOT NULL,
  "external_id" text NOT NULL UNIQUE,
  "is_active" boolean NOT NULL,
  "admin_only" boolean NOT NULL,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

-- Create coupons table
CREATE TABLE IF NOT EXISTS "coupons" (
  "id" SERIAL PRIMARY KEY,
  "code" TEXT NOT NULL,
  "type" TEXT NOT NULL CHECK (type IN ('percentage', 'fixed')),
  "value" INTEGER NOT NULL CHECK (value > 0),
  "quantity" INT NOT NULL,
  "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
  "expires_at" TIMESTAMP NOT NULL,
  "min_order_value" INTEGER NOT NULL DEFAULT 1500 CHECK ("min_order_value" >= 1500),
  "owner_type" TEXT NOT NULL CHECK ("owner_type" IN ('admin', 'company')),
  "owner_id" INTEGER NOT NULL,
  "external_id" TEXT NOT NULL UNIQUE,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


-- Add comments for documentation
COMMENT ON COLUMN coupons.value IS 'Coupon value in cents (e.g., 250 = R$ 2.50)';
COMMENT ON COLUMN coupons.min_order_value IS 'Minimum order value in cents (e.g., 1500 = R$ 15.00)';

-- Create users_coupons table with foreign key to coupons.id
CREATE TABLE IF NOT EXISTS "users_coupons" (
  "id" BIGSERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "coupon_id" INTEGER NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
  "used_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT users_coupons_unique UNIQUE (user_id, coupon_id),
  FOREIGN KEY (coupon_id) REFERENCES coupons(id)
);

-- Create invoices table with user_id and company_id foreign keys
CREATE TABLE IF NOT EXISTS "invoices" (
  "id" BIGSERIAL PRIMARY KEY,
  "user_id" integer REFERENCES users(id) NOT NULL,
  "company_id" integer REFERENCES companies(id) NOT NULL,
  "status" text NOT NULL CHECK (status IN ('pending', 'failed', 'expired', 'processing', 'preparing', 'ready', 'delivering', 'completed')),
  "order_id" text NOT NULL UNIQUE,
  "payment_method" text NOT NULL,
  "amount" integer NOT NULL,
  "discount" integer NOT NULL DEFAULT 0,
  "shipping_fee" INTEGER NOT NULL DEFAULT 0 CHECK (shipping_fee >= 0),
  "delivery_mode" text NOT NULL CHECK (delivery_mode IN ('delivery', 'pickup')),
  "coupon" text,
  "user_address" text,
  "user_phone_number" text,
  "info" text,
  "info_details" jsonb,
  "finished_at" timestamp,
  "created_at" timestamp NOT NULL DEFAULT (now()),
  "updated_at" timestamp NOT NULL DEFAULT (now())
);

-- Add comment for documentation
COMMENT ON COLUMN invoices.shipping_fee IS 'Shipping fee at time of invoice creation in cents (e.g., 500 = R$ 5.00)';


-- Create invoice_products table to store individual product line items
CREATE TABLE IF NOT EXISTS "invoice_products" (
  "id" BIGSERIAL PRIMARY KEY,
  "invoice_id" BIGINT NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  "product_id" INTEGER NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
  "quantity" INTEGER NOT NULL CHECK (quantity > 0),
  "unit_price" INTEGER NOT NULL CHECK (unit_price >= 0), -- Price at time of purchase in cents
  "discount" INTEGER NOT NULL DEFAULT 0 CHECK (discount >= 0), -- Individual product discount in cents
  "product_name" TEXT NOT NULL, -- Store product name at time of purchase
  "product_ean" TEXT NOT NULL, -- Store EAN at time of purchase
  "product_external_id" TEXT NOT NULL, -- Store external_id at time of purchase
  "product_brand" TEXT, -- Store brand at time of purchase
  "product_image" TEXT, -- Store image URL at time of purchase
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMP NOT NULL DEFAULT (now()),
  
  -- Ensure we don't have duplicate products in the same invoice
  UNIQUE(invoice_id, product_id)
);

-- Add comments for documentation
COMMENT ON TABLE invoice_products IS 'Normalized invoice line items replacing the JSONB products column';
COMMENT ON COLUMN invoice_products.unit_price IS 'Product price at time of purchase in cents (e.g., 1000 = R$ 10.00)';
COMMENT ON COLUMN invoice_products.discount IS 'Individual product discount in cents (e.g., 250 = R$ 2.50)';
COMMENT ON COLUMN invoice_products.product_name IS 'Product name stored at time of purchase for historical accuracy';
COMMENT ON COLUMN invoice_products.product_ean IS 'Product EAN stored at time of purchase for historical accuracy';
COMMENT ON COLUMN invoice_products.product_external_id IS 'Product external_id stored at time of purchase for historical accuracy';

CREATE TABLE IF NOT EXISTS "sessions" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer REFERENCES users(id) NOT NULL,
  "access_token" text NOT NULL,
  "refresh_token" text NOT NULL,
  "is_active" BOOLEAN NOT NULL DEFAULT true,
  "device" TEXT NOT NULL,
  "ip" TEXT NOT NULL,
  "application_type" TEXT NOT NULL DEFAULT 'mobile' CHECK (application_type IN ('mobile', 'partner-web')),
  "created_at" TIMESTAMP NOT NULL DEFAULT (now()),
  "ended_at" TIMESTAMP NOT NULL DEFAULT (now())
);

CREATE TABLE IF NOT EXISTS "company_active_campaigns" (
  "company_id" INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  "campaign_id" INTEGER NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
  PRIMARY KEY (company_id, campaign_id)
);

-- Lista de produtos criada por usuários
CREATE TABLE IF NOT EXISTS "user_products_lists" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  "name" TEXT NOT NULL,
  "icon_url" TEXT NOT NULL,
  "external_id" TEXT NOT NULL UNIQUE,
  "is_public" BOOLEAN NOT NULL DEFAULT false,
  "created_at" TIMESTAMP NOT NULL DEFAULT now(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
  UNIQUE(user_id, name)
);

-- Relação N:N entre listas de usuário e produtos
CREATE TABLE IF NOT EXISTS "user_products_lists_items" (
  "list_id" INTEGER NOT NULL REFERENCES user_products_lists(id) ON DELETE CASCADE,
  "product_id" INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  "quantity" INTEGER NOT NULL DEFAULT 1,
  PRIMARY KEY (list_id, product_id)
);

-- Lista de produtos modelo (template)
CREATE TABLE IF NOT EXISTS "templates_products_lists" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "is_active" BOOLEAN NOT NULL DEFAULT true,
  "external_id" TEXT NOT NULL UNIQUE,
  "created_at" TIMESTAMP NOT NULL DEFAULT now(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT now()
);

-- Relação N:N entre listas modelo e produtos
CREATE TABLE IF NOT EXISTS "templates_products_lists_items" (
  "template_id" INTEGER NOT NULL REFERENCES templates_products_lists(id) ON DELETE CASCADE,
  "product_id" INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  "quantity" INTEGER NOT NULL DEFAULT 1,
  PRIMARY KEY (template_id, product_id)
);

