-- Update company_products primary key to include reference so the same product can be added with different references
-- 1) Backfill null references to a default value to satisfy NOT NULL
UPDATE company_products SET reference = 'un' WHERE reference IS NULL;

-- 2) Ensure reference is NOT NULL moving forward
ALTER TABLE company_products ALTER COLUMN reference SET NOT NULL;

-- 3) Drop existing unique index that conflicts with new structure
DROP INDEX IF EXISTS idx_company_products_unique;

-- 4) Drop existing primary key (company_id, product_id)
ALTER TABLE company_products DROP CONSTRAINT IF EXISTS company_products_pkey;

-- 5) Create new primary key including reference
ALTER TABLE company_products ADD CONSTRAINT company_products_pkey PRIMARY KEY (company_id, product_id, reference);

-- 6) Create new unique index for the ON CONFLICT clause
CREATE UNIQUE INDEX idx_company_products_unique ON company_products (company_id, product_id, reference);

