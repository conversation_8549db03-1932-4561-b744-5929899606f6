import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { minMobileVersionService } from "@/services/api";
import { MinMobileVersionRequest } from "@/types/api";
import { toast } from "sonner";
import { Smartphone, Loader2, CheckCircle2, AlertTriangle, Power } from "lucide-react";

const MinMobileVersion = () => {
  const queryClient = useQueryClient();

  // Default values for first-time setup
  const DEFAULT_VERSION = "1.0.0";

  // Form state
  const [formData, setFormData] = useState({
    ios: DEFAULT_VERSION,
    android: DEFAULT_VERSION
  });

  // Validation state
  const [validationErrors, setValidationErrors] = useState({
    ios: "",
    android: ""
  });

  // State to track if this is first-time setup
  const [isFirstTimeSetup, setIsFirstTimeSetup] = useState(false);

  // State to track activation status
  const [isActivated, setIsActivated] = useState(true);

  // Fetch current minimum versions
  const { data: currentVersions, isLoading: isLoadingVersions, error } = useQuery({
    queryKey: ["min-mobile-version"],
    queryFn: minMobileVersionService.getMinMobileVersion,
    retry: (failureCount, error: any) => {
      // Don't retry if it's the specific "no rows" error
      if (error?.response?.status === 500 &&
          error?.response?.data?.message === "no rows in result set") {
        return false;
      }
      // Retry other errors up to 3 times
      return failureCount < 3;
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: (data: MinMobileVersionRequest) => minMobileVersionService.updateMinMobileVersion(data),
    onSuccess: () => {
      const successMessage = isFirstTimeSetup
        ? "Versões mínimas configuradas com sucesso!"
        : "Versões mínimas atualizadas com sucesso!";

      toast(successMessage, {
        dismissible: true,
      });
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ["min-mobile-version"] });
    },
    onError: (error: any) => {
      const baseErrorMessage = isFirstTimeSetup
        ? "Erro ao configurar versões mínimas"
        : "Erro ao atualizar versões mínimas";

      const errorMessage = error.response?.data?.message || baseErrorMessage;
      toast(errorMessage, {
        dismissible: true,
      });
    },
  });

  // Toggle activation mutation
  const toggleActivationMutation = useMutation({
    mutationFn: (isActive: boolean) => minMobileVersionService.toggleActivation(isActive),
    onSuccess: (_, isActive) => {
      setIsActivated(isActive);
      const message = isActive
        ? "Funcionalidade de versão mínima ativada com sucesso!"
        : "Funcionalidade de versão mínima desativada com sucesso!";

      toast(message, {
        dismissible: true,
      });

      // Invalidate and refetch data when reactivating
      if (isActive) {
        queryClient.invalidateQueries({ queryKey: ["min-mobile-version"] });
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || "Erro ao alterar status da funcionalidade";
      toast(errorMessage, {
        dismissible: true,
      });
    },
  });

  // SEMVER validation regex
  const semverRegex = /^\d+\.\d+\.\d+$/;

  // Validate SEMVER format
  const validateVersion = (version: string, _platform: 'ios' | 'android') => {
    if (!version.trim()) {
      return "Este campo é obrigatório";
    }
    if (!semverRegex.test(version.trim())) {
      return "Formato inválido. Use o formato X.Y.Z (ex: 1.2.3)";
    }
    return "";
  };

  // Handle input change with validation
  const handleInputChange = (platform: 'ios' | 'android', value: string) => {
    setFormData(prev => ({
      ...prev,
      [platform]: value
    }));

    // Validate on change
    const error = validateVersion(value, platform);
    setValidationErrors(prev => ({
      ...prev,
      [platform]: error
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const iosError = validateVersion(formData.ios, 'ios');
    const androidError = validateVersion(formData.android, 'android');

    setValidationErrors({
      ios: iosError,
      android: androidError
    });

    // Check if there are any validation errors
    if (iosError || androidError) {
      toast("Corrija os erros no formulário antes de continuar", {
        dismissible: true,
      });
      return;
    }

    // Submit the form
    updateMutation.mutate({
      ios: formData.ios.trim(),
      android: formData.android.trim()
    });
  };

  // Check if this is the specific "no rows" error for first-time setup
  const isNoDataError = error?.response?.status === 500 &&
                        error?.response?.data?.message === "no rows in result set";

  // Check if functionality is disabled (500 error with empty data)
  const isFunctionalityDisabled = error?.response?.status === 500 &&
                                  error?.response?.data?.data?.ios === "" &&
                                  error?.response?.data?.data?.android === "";

  // Check if form is valid
  const isFormValid = !validationErrors.ios && !validationErrors.android &&
                     formData.ios.trim() && formData.android.trim();

  // Handle toggle activation
  const handleToggleActivation = (checked: boolean) => {
    toggleActivationMutation.mutate(checked);
  };

  // Set initial form data when versions are loaded or handle first-time setup
  React.useEffect(() => {
    if (currentVersions?.data) {
      // Data exists, populate with actual values
      setFormData({
        ios: currentVersions.data.ios,
        android: currentVersions.data.android
      });
      setIsFirstTimeSetup(false);
      setIsActivated(true);
    } else if (isNoDataError) {
      // First-time setup: no data exists, use defaults
      setFormData({
        ios: DEFAULT_VERSION,
        android: DEFAULT_VERSION
      });
      setIsFirstTimeSetup(true);
      setIsActivated(true);
    } else if (isFunctionalityDisabled) {
      // Functionality is disabled
      setIsActivated(false);
    }
  }, [currentVersions, isNoDataError, isFunctionalityDisabled, DEFAULT_VERSION]);

  // Only show error for non-"no rows" errors and non-disabled functionality errors
  if (error && !isNoDataError && !isFunctionalityDisabled) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
                <div className="text-center">
                  <h3 className="text-lg font-medium">Erro ao carregar dados</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    Não foi possível carregar as versões mínimas atuais.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Versão Mínima Mobile</h2>
          <p className="text-muted-foreground">
            {isActivated
              ? isFirstTimeSetup
                ? "Primeira configuração - defina as versões mínimas iniciais"
                : "Configure as versões mínimas exigidas para os aplicativos iOS e Android"
              : "Funcionalidade desativada - ative para gerenciar versões mínimas"
            }
          </p>
        </div>

        {/* Toggle Switch */}
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Power className={`h-5 w-5 ${isActivated ? 'text-green-600' : 'text-gray-400'}`} />
            <div className="flex flex-col">
              <Label htmlFor="activation-toggle" className="text-sm font-medium">
                {isActivated ? 'Ativado' : 'Desativado'}
              </Label>
              <p className="text-xs text-muted-foreground">
                {isActivated ? 'Funcionalidade ativa' : 'Funcionalidade inativa'}
              </p>
            </div>
            <Switch
              id="activation-toggle"
              checked={isActivated}
              onCheckedChange={handleToggleActivation}
              disabled={toggleActivationMutation.isPending}
            />
          </div>
        </Card>
      </div>

      {/* Disabled Functionality Message */}
      {!isActivated && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4 py-8">
              <Power className="h-16 w-16 text-gray-400" />
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900">Funcionalidade Desativada</h3>
                <p className="text-sm text-muted-foreground mt-2 max-w-md">
                  A funcionalidade de versão mínima mobile está atualmente desativada.
                  Ative o controle acima para gerenciar as versões mínimas dos aplicativos iOS e Android.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Versions Display */}
      {isActivated && (currentVersions?.data || isFirstTimeSetup) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {isFirstTimeSetup ? "Versão Padrão iOS" : "Versão Atual iOS"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {currentVersions?.data?.ios || DEFAULT_VERSION}
                {isFirstTimeSetup && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    (padrão)
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {isFirstTimeSetup ? "Versão Padrão Android" : "Versão Atual Android"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {currentVersions?.data?.android || DEFAULT_VERSION}
                {isFirstTimeSetup && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    (padrão)
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Update Form - Only show when activated */}
      {isActivated && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Smartphone className="mr-2" size={20} />
              {isFirstTimeSetup ? "Configurar Versões Mínimas Iniciais" : "Atualizar Versões Mínimas"}
            </CardTitle>
          </CardHeader>
          <CardContent>
          {isLoadingVersions ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Carregando versões atuais...</span>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {isFirstTimeSetup && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <CheckCircle2 className="h-5 w-5 text-blue-600 mt-0.5" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-800">
                        <strong>Primeira configuração:</strong> Os valores padrão (1.0.0) foram definidos.
                        Você pode editá-los conforme necessário antes de salvar.
                      </p>
                    </div>
                  </div>
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* iOS Version Input */}
                <div className="space-y-2">
                  <Label htmlFor="ios">Versão Mínima iOS *</Label>
                  <Input
                    id="ios"
                    value={formData.ios}
                    onChange={(e) => handleInputChange('ios', e.target.value)}
                    placeholder="1.0.0"
                    className={validationErrors.ios ? "border-red-500" : ""}
                    disabled={updateMutation.isPending}
                  />
                  {validationErrors.ios && (
                    <p className="text-sm text-red-500">{validationErrors.ios}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Formato: X.Y.Z (exemplo: 1.2.3)
                  </p>
                </div>

                {/* Android Version Input */}
                <div className="space-y-2">
                  <Label htmlFor="android">Versão Mínima Android *</Label>
                  <Input
                    id="android"
                    value={formData.android}
                    onChange={(e) => handleInputChange('android', e.target.value)}
                    placeholder="1.0.0"
                    className={validationErrors.android ? "border-red-500" : ""}
                    disabled={updateMutation.isPending}
                  />
                  {validationErrors.android && (
                    <p className="text-sm text-red-500">{validationErrors.android}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Formato: X.Y.Z (exemplo: 1.2.3)
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={updateMutation.isPending || !isFormValid}
                  className="min-w-[120px]"
                >
                  {updateMutation.isPending ? (
                    <>
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      {isFirstTimeSetup ? "Configurando..." : "Atualizando..."}
                    </>
                  ) : (
                    <>
                      <CheckCircle2 size={16} className="mr-2" />
                      {isFirstTimeSetup ? "CONFIGURAR" : "ATUALIZAR"}
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
      )}

      {/* Help Information - Only show when activated */}
      {isActivated && (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações Importantes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm text-muted-foreground">
            <p className="mb-2">
              <strong>Formato de Versão:</strong> Use o formato SEMVER (Semantic Versioning) X.Y.Z onde:
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li><strong>X</strong> = Versão principal (major)</li>
              <li><strong>Y</strong> = Versão secundária (minor)</li>
              <li><strong>Z</strong> = Correção (patch)</li>
            </ul>
            <p className="mt-3">
              <strong>Exemplo:</strong> 1.2.3 significa versão principal 1, versão secundária 2, correção 3.
            </p>
            <p className="mt-3">
              <strong>Importante:</strong> Usuários com versões inferiores às configuradas aqui serão obrigados a atualizar o aplicativo.
            </p>
            {isFirstTimeSetup && (
              <p className="mt-3 text-blue-700">
                <strong>Primeira configuração:</strong> Esta é a primeira vez que as versões mínimas estão sendo definidas.
                Os valores padrão (1.0.0) podem ser ajustados conforme necessário.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
      )}
    </div>
  );
};

export default MinMobileVersion;
