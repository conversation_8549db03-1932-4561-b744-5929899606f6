package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"slices"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
	"github.com/stretchr/testify/assert"
)

// MockQueriesInterface defines the interface we need for testing
type MockQueriesInterface interface {
	GetCompaniesByOwnerID(ctx context.Context, ownerID sql.NullInt32) ([]string, error)
}

// MockQueries is a simple mock implementation
type MockQueries struct {
	companies []string
	err       error
}

func (m *MockQueries) GetCompaniesByOwnerID(ctx context.Context, ownerID sql.NullInt32) ([]string, error) {
	return m.companies, m.err
}

// testCompanyHandler is a test version of CompanyHandler that uses our mock
type testCompanyHandler struct {
	mockQueries *MockQueries
}

// GetMyCompanies is a simplified version for testing
func (h *testCompanyHandler) GetMyCompanies(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		http.Error(w, `{"success":false,"error":"user not authenticated"}`, http.StatusUnauthorized)
		return
	}

	// Get companies owned by this user
	companyExternalIDs, err := h.mockQueries.GetCompaniesByOwnerID(ctx, sql.NullInt32{Int32: user.ID, Valid: true})
	if err != nil {
		http.Error(w, `{"success":false,"error":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// If no companies found, return empty array
	if companyExternalIDs == nil {
		companyExternalIDs = []string{}
	}

	response := struct {
		Success bool `json:"success"`
		Data    struct {
			CompanyExternalIDs []string `json:"company_external_ids"`
		} `json:"data"`
	}{
		Success: true,
	}
	response.Data.CompanyExternalIDs = companyExternalIDs

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestGetMyCompanies(t *testing.T) {
	tests := []struct {
		name           string
		userID         int32
		userExternalID string
		mockReturn     []string
		mockError      error
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "User with companies",
			userID:         1,
			userExternalID: "user-123",
			mockReturn:     []string{"company-1", "company-2", "company-3"},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedCount:  3,
		},
		{
			name:           "User with no companies",
			userID:         2,
			userExternalID: "user-456",
			mockReturn:     []string{},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedCount:  0,
		},
		{
			name:           "User with nil companies",
			userID:         3,
			userExternalID: "user-789",
			mockReturn:     nil,
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedCount:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockQueries{
				companies: tt.mockReturn,
				err:       tt.mockError,
			}

			// Create a test handler that uses our mock
			testHandler := &testCompanyHandler{
				mockQueries: mockQueries,
			}

			// Create request
			req := httptest.NewRequest("GET", "/v1/company/my-companies", nil)

			// Add user to context
			user := middlewares.User{
				ID:         tt.userID,
				ExternalID: tt.userExternalID,
				IsActive:   true,
			}
			ctx := context.WithValue(req.Context(), middlewares.UserContextKey, user)
			req = req.WithContext(ctx)

			// Create response recorder
			w := httptest.NewRecorder()

			// Call handler
			testHandler.GetMyCompanies(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					Success bool `json:"success"`
					Data    struct {
						CompanyExternalIDs []string `json:"company_external_ids"`
					} `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.True(t, response.Success)
				assert.Equal(t, tt.expectedCount, len(response.Data.CompanyExternalIDs))

				// Check actual company IDs if expected
				if len(tt.mockReturn) > 0 {
					assert.Equal(t, tt.mockReturn, response.Data.CompanyExternalIDs)
				}
			}
		})
	}
}

func TestGetMyCompanies_NoUserInContext(t *testing.T) {
	// Create handler
	handler := &CompanyHandler{}

	// Create request without user in context
	req := httptest.NewRequest("GET", "/v1/company/my-companies", nil)
	w := httptest.NewRecorder()

	// Call handler
	handler.GetMyCompanies(w, req)

	// Check status code
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	// Parse response (using the actual ErrorResponse format)
	var response struct {
		Code    string `json:"code"`
		Message string `json:"message"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check response structure
	assert.Equal(t, "001", response.Code)
	assert.Contains(t, response.Message, "user not authenticated")
}

// MockCompanyDetailsQueries is a mock for testing GetCompanyDetails
type MockCompanyDetailsQueries struct {
	companyDetails postgres.GetCompanyDetailsWithOwnerRow
	err            error
}

func (m *MockCompanyDetailsQueries) GetCompanyDetailsWithOwner(ctx context.Context, externalID string) (postgres.GetCompanyDetailsWithOwnerRow, error) {
	return m.companyDetails, m.err
}

// testCompanyDetailsHandler is a test version of CompanyHandler for GetCompanyDetails
type testCompanyDetailsHandler struct {
	mockQueries *MockCompanyDetailsQueries
}

// GetCompanyDetails is a simplified version for testing
func (h *testCompanyDetailsHandler) GetCompanyDetails(w http.ResponseWriter, r *http.Request) {
	// Get external_id parameter from URL path
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		http.Error(w, `{"code":"001","message":"external_id is required"}`, http.StatusBadRequest)
		return
	}

	// Validate external_id format (ULID should be 26 characters)
	if len(externalID) != 26 {
		http.Error(w, `{"code":"001","message":"invalid external_id format"}`, http.StatusBadRequest)
		return
	}

	// Get company details with owner information
	companyDetails, err := h.mockQueries.GetCompanyDetailsWithOwner(r.Context(), externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, `{"code":"001","message":"company not found or inactive"}`, http.StatusNotFound)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Handle nullable subscription_id
	var subscriptionID *int32
	if companyDetails.SubscriptionID.Valid {
		subscriptionID = &companyDetails.SubscriptionID.Int32
	}

	// Handle owner details (nullable)
	var owner *CompanyOwnerDetails
	if companyDetails.OwnerExternalID.Valid && companyDetails.OwnerName.Valid && companyDetails.OwnerEmail.Valid {
		owner = &CompanyOwnerDetails{
			ExternalID: companyDetails.OwnerExternalID.String,
			Name:       companyDetails.OwnerName.String,
			Email:      companyDetails.OwnerEmail.String,
		}
	}

	// Parse addresses JSON
	var addresses []interface{}
	if companyDetails.Addresses.Bytes != nil {
		if err := json.Unmarshal(companyDetails.Addresses.Bytes, &addresses); err != nil {
			// Set empty array if parsing fails
			addresses = []interface{}{}
		}
	} else {
		addresses = []interface{}{}
	}

	// Parse products JSON (simplified for testing)
	var products []interface{}
	if companyDetails.Products.Bytes != nil {
		if err := json.Unmarshal(companyDetails.Products.Bytes, &products); err != nil {
			// Set empty array if parsing fails
			products = []interface{}{}
		}
	} else {
		products = []interface{}{}
	}

	// Build response
	response := struct {
		Data GetCompanyDetailsResponse `json:"data"`
	}{
		Data: GetCompanyDetailsResponse{
			ID:               companyDetails.ID,
			Name:             companyDetails.Name,
			Cnpj:             companyDetails.Cnpj,
			Bio:              companyDetails.Bio,
			Picture:          companyDetails.Picture,
			PhoneNumbers:     companyDetails.PhoneNumbers,
			PixKey:           companyDetails.PixKey,
			SubscriptionID:   subscriptionID,
			Rating:           companyDetails.Rating,
			ShippingFee:      companyDetails.ShippingFee,
			AffiliateBalance: companyDetails.AffiliateBalance,
			DeliveryModes:    companyDetails.DeliveryModes,
			ExternalID:       companyDetails.ExternalID,
			IsActive:         companyDetails.IsActive,
			CommissionRate:   companyDetails.CommissionRate,
			CashbackRate:     companyDetails.CashbackRate,
			CreatedAt:        companyDetails.CreatedAt.Format(time.RFC3339),
			UpdatedAt:        companyDetails.UpdatedAt.Format(time.RFC3339),
			Owner:            owner,
			Addresses:        addresses,
			Products:         []custom_models.Product{}, // Simplified for testing
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestGetCompanyDetails(t *testing.T) {
	// Create sample addresses JSON
	addressesJSON := `[{"external_id":"addr-123","name":"Main Office","street":"123 Main St","number":"100","city":"São Paulo","state":"SP"}]`

	// Create sample products JSON
	productsJSON := `[{"name":"Test Product","ean":"1234567890123","external_id":"prod-123","price":1000,"stock":50,"discount":0}]`

	tests := []struct {
		name           string
		externalID     string
		mockData       postgres.GetCompanyDetailsWithOwnerRow
		mockError      error
		expectedStatus int
		hasOwner       bool
	}{
		{
			name:       "Successful retrieval with owner",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			mockData: postgres.GetCompanyDetailsWithOwnerRow{
				ID:               1,
				Name:             "Test Company",
				Cnpj:             "12345678000195",
				Bio:              "Test company bio",
				Picture:          "https://example.com/image.jpg",
				PhoneNumbers:     []string{"11999999999"},
				PixKey:           "<EMAIL>",
				SubscriptionID:   sql.NullInt32{Int32: 1, Valid: true},
				Rating:           4.5,
				ShippingFee:      1000,
				AffiliateBalance: 5000,
				DeliveryModes:    []string{"delivery", "pickup"},
				ExternalID:       "01JR0X8RNYFJECMV1NBNVK9921",
				IsActive:         true,
				CommissionRate:   1500,
				CashbackRate:     200,
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
				OwnerExternalID:  sql.NullString{String: "owner-123", Valid: true},
				OwnerName:        sql.NullString{String: "John Doe", Valid: true},
				OwnerEmail:       sql.NullString{String: "<EMAIL>", Valid: true},
				Addresses:        pgtype.JSON{Bytes: []byte(addressesJSON), Status: pgtype.Present},
				Products:         pgtype.JSON{Bytes: []byte(productsJSON), Status: pgtype.Present},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			hasOwner:       true,
		},
		{
			name:       "Successful retrieval without owner",
			externalID: "01JR0X8RNYFJECMV1NBNVK9922",
			mockData: postgres.GetCompanyDetailsWithOwnerRow{
				ID:               2,
				Name:             "Test Company 2",
				Cnpj:             "98765432000195",
				Bio:              "Test company bio 2",
				Picture:          "https://example.com/image2.jpg",
				PhoneNumbers:     []string{"11888888888"},
				PixKey:           "<EMAIL>",
				SubscriptionID:   sql.NullInt32{Valid: false},
				Rating:           5.0,
				ShippingFee:      500,
				AffiliateBalance: 0,
				DeliveryModes:    []string{"pickup"},
				ExternalID:       "01JR0X8RNYFJECMV1NBNVK9922",
				IsActive:         true,
				CommissionRate:   1000,
				CashbackRate:     100,
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
				OwnerExternalID:  sql.NullString{Valid: false},
				OwnerName:        sql.NullString{Valid: false},
				OwnerEmail:       sql.NullString{Valid: false},
				Addresses:        pgtype.JSON{Status: pgtype.Null},
				Products:         pgtype.JSON{Status: pgtype.Null},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			hasOwner:       false,
		},
		{
			name:           "Company not found",
			externalID:     "01JR0X8RNYFJECMV1NBNVK9999",
			mockData:       postgres.GetCompanyDetailsWithOwnerRow{},
			mockError:      sql.ErrNoRows,
			expectedStatus: http.StatusNotFound,
			hasOwner:       false,
		},
		{
			name:           "Invalid external_id format - too short",
			externalID:     "short",
			mockData:       postgres.GetCompanyDetailsWithOwnerRow{},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			hasOwner:       false,
		},
		{
			name:           "Invalid external_id format - too long",
			externalID:     "01JR0X8RNYFJECMV1NBNVK9921EXTRA",
			mockData:       postgres.GetCompanyDetailsWithOwnerRow{},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
			hasOwner:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockCompanyDetailsQueries{
				companyDetails: tt.mockData,
				err:            tt.mockError,
			}

			// Create test handler
			testHandler := &testCompanyDetailsHandler{
				mockQueries: mockQueries,
			}

			// Create router and add route
			router := chi.NewRouter()
			router.Get("/{external_id}/details", testHandler.GetCompanyDetails)

			// Create request
			req := httptest.NewRequest("GET", "/"+tt.externalID+"/details", nil)
			w := httptest.NewRecorder()

			// Call handler
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					Data GetCompanyDetailsResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.mockData.Name, response.Data.Name)
				assert.Equal(t, tt.mockData.ExternalID, response.Data.ExternalID)
				assert.Equal(t, tt.mockData.IsActive, response.Data.IsActive)

				// Check owner details
				if tt.hasOwner {
					assert.NotNil(t, response.Data.Owner)
					assert.Equal(t, "owner-123", response.Data.Owner.ExternalID)
					assert.Equal(t, "John Doe", response.Data.Owner.Name)
					assert.Equal(t, "<EMAIL>", response.Data.Owner.Email)
				} else {
					assert.Nil(t, response.Data.Owner)
				}

				// Check addresses
				assert.NotNil(t, response.Data.Addresses)
				if tt.hasOwner {
					assert.Greater(t, len(response.Data.Addresses), 0)
				}
			}
		})
	}
}

// MockUpdateCompanyStatusQueries is a mock for testing UpdateCompanyStatus
type MockUpdateCompanyStatusQueries struct {
	companyDetails postgres.GetCompanyByExternalIDRow
	userDetails    postgres.GetUserByIDRow
	updatedCompany postgres.UpdateCompanyStatusRow
	getCompanyErr  error
	getUserErr     error
	updateErr      error
}

func (m *MockUpdateCompanyStatusQueries) GetCompanyByExternalID(ctx context.Context, externalID string) (postgres.GetCompanyByExternalIDRow, error) {
	return m.companyDetails, m.getCompanyErr
}

func (m *MockUpdateCompanyStatusQueries) GetUserByID(ctx context.Context, id int32) (postgres.GetUserByIDRow, error) {
	return m.userDetails, m.getUserErr
}

func (m *MockUpdateCompanyStatusQueries) UpdateCompanyStatus(ctx context.Context, arg postgres.UpdateCompanyStatusParams) (postgres.UpdateCompanyStatusRow, error) {
	return m.updatedCompany, m.updateErr
}

// testUpdateCompanyStatusHandler is a test version of CompanyHandler for UpdateCompanyStatus
type testUpdateCompanyStatusHandler struct {
	mockQueries *MockUpdateCompanyStatusQueries
}

// UpdateCompanyStatus is a simplified version for testing
func (h *testUpdateCompanyStatusHandler) UpdateCompanyStatus(w http.ResponseWriter, r *http.Request) {
	// Get externalID parameter from URL path
	externalID := chi.URLParam(r, "externalID")
	if externalID == "" {
		http.Error(w, `{"code":"001","message":"invalid companyExternalID"}`, http.StatusBadRequest)
		return
	}

	// Parse request body
	var payload UpdateCompanyStatusRequest
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, `{"code":"001","message":"invalid request body"}`, http.StatusBadRequest)
		return
	}

	// Get company details
	company, err := h.mockQueries.GetCompanyByExternalID(r.Context(), externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, `{"code":"001","message":"company not found"}`, http.StatusNotFound)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Only validate owner when activating
	if payload.Activate != nil && *payload.Activate {
		if !company.OwnerID.Valid {
			http.Error(w, `{"code":"001","message":"company cannot be activated without a valid owner"}`, http.StatusBadRequest)
			return
		}

		// Validate the owner exists and is active
		ownerInfo, err := h.mockQueries.GetUserByID(r.Context(), company.OwnerID.Int32)
		if err != nil {
			if err == sql.ErrNoRows {
				http.Error(w, `{"code":"001","message":"company cannot be activated without a valid owner"}`, http.StatusBadRequest)
				return
			}
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		if !ownerInfo.IsActive {
			http.Error(w, `{"code":"001","message":"company cannot be activated without a valid owner"}`, http.StatusBadRequest)
			return
		}
	}

	// Update company status
	updatedCompany, err := h.mockQueries.UpdateCompanyStatus(r.Context(), postgres.UpdateCompanyStatusParams{
		ExternalID: externalID,
		IsActive:   *payload.Activate,
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Build response
	response := struct {
		Data ActivateCompanyResponse `json:"data"`
	}{
		Data: ActivateCompanyResponse{
			ExternalID: updatedCompany.ExternalID,
			Name:       updatedCompany.Name,
			IsActive:   updatedCompany.IsActive,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestUpdateCompanyStatus(t *testing.T) {
	tests := []struct {
		name           string
		externalID     string
		requestBody    UpdateCompanyStatusRequest
		mockCompany    postgres.GetCompanyByExternalIDRow
		mockUser       postgres.GetUserByIDRow
		mockUpdated    postgres.UpdateCompanyStatusRow
		getCompanyErr  error
		getUserErr     error
		updateErr      error
		expectedStatus int
		expectedActive bool
	}{
		{
			name:       "Successfully activate company with valid owner",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: UpdateCompanyStatusRequest{
				Activate: &[]bool{true}[0],
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:       1,
				Name:     "Test Company",
				OwnerID:  sql.NullInt32{Int32: 1, Valid: true},
				IsActive: false,
			},
			mockUser: postgres.GetUserByIDRow{
				ID:       1,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdated: postgres.UpdateCompanyStatusRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				IsActive:   true,
			},
			getCompanyErr:  nil,
			getUserErr:     nil,
			updateErr:      nil,
			expectedStatus: http.StatusOK,
			expectedActive: true,
		},
		{
			name:       "Successfully deactivate company",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: UpdateCompanyStatusRequest{
				Activate: &[]bool{false}[0],
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:       1,
				Name:     "Test Company",
				OwnerID:  sql.NullInt32{Int32: 1, Valid: true},
				IsActive: true,
			},
			mockUser: postgres.GetUserByIDRow{}, // Not needed for deactivation
			mockUpdated: postgres.UpdateCompanyStatusRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				IsActive:   false,
			},
			getCompanyErr:  nil,
			getUserErr:     nil,
			updateErr:      nil,
			expectedStatus: http.StatusOK,
			expectedActive: false,
		},
		{
			name:       "Fail to activate company without owner",
			externalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: UpdateCompanyStatusRequest{
				Activate: &[]bool{true}[0],
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:       1,
				Name:     "Test Company",
				OwnerID:  sql.NullInt32{Valid: false}, // No owner
				IsActive: false,
			},
			mockUser:       postgres.GetUserByIDRow{},
			mockUpdated:    postgres.UpdateCompanyStatusRow{},
			getCompanyErr:  nil,
			getUserErr:     nil,
			updateErr:      nil,
			expectedStatus: http.StatusBadRequest,
			expectedActive: false,
		},
		{
			name:       "Company not found",
			externalID: "01JR0X8RNYFJECMV1NBNVK9999",
			requestBody: UpdateCompanyStatusRequest{
				Activate: &[]bool{true}[0],
			},
			mockCompany:    postgres.GetCompanyByExternalIDRow{},
			mockUser:       postgres.GetUserByIDRow{},
			mockUpdated:    postgres.UpdateCompanyStatusRow{},
			getCompanyErr:  sql.ErrNoRows,
			getUserErr:     nil,
			updateErr:      nil,
			expectedStatus: http.StatusNotFound,
			expectedActive: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockUpdateCompanyStatusQueries{
				companyDetails: tt.mockCompany,
				userDetails:    tt.mockUser,
				updatedCompany: tt.mockUpdated,
				getCompanyErr:  tt.getCompanyErr,
				getUserErr:     tt.getUserErr,
				updateErr:      tt.updateErr,
			}

			// Create test handler
			testHandler := &testUpdateCompanyStatusHandler{
				mockQueries: mockQueries,
			}

			// Create router and add route
			router := chi.NewRouter()
			router.Post("/{externalID}/status", testHandler.UpdateCompanyStatus)

			// Create request body
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/"+tt.externalID+"/status", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Call handler
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					Data ActivateCompanyResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.mockUpdated.Name, response.Data.Name)
				assert.Equal(t, tt.mockUpdated.ExternalID, response.Data.ExternalID)
				assert.Equal(t, tt.expectedActive, response.Data.IsActive)
			}
		})
	}
}

// MockLinkUserToCompanyQueries is a mock for testing LinkUserToCompany with role management
type MockLinkUserToCompanyQueries struct {
	company           postgres.GetCompanyByExternalIDRow
	user              postgres.GetUserByExternalIDRow
	updatedCompany    postgres.UpdateCompanyOwnerRow
	hasPartnerRole    bool
	companyCount      int64
	getCompanyError   error
	getUserError      error
	updateOwnerError  error
	checkRoleError    error
	assignRoleError   error
	removeRoleError   error
	countCompanyError error
}

func (m *MockLinkUserToCompanyQueries) GetCompanyByExternalID(ctx context.Context, externalID string) (postgres.GetCompanyByExternalIDRow, error) {
	return m.company, m.getCompanyError
}

func (m *MockLinkUserToCompanyQueries) GetUserByExternalID(ctx context.Context, externalID string) (postgres.GetUserByExternalIDRow, error) {
	return m.user, m.getUserError
}

func (m *MockLinkUserToCompanyQueries) UpdateCompanyOwner(ctx context.Context, arg postgres.UpdateCompanyOwnerParams) (postgres.UpdateCompanyOwnerRow, error) {
	return m.updatedCompany, m.updateOwnerError
}

func (m *MockLinkUserToCompanyQueries) CheckIfUserHasRole(ctx context.Context, arg postgres.CheckIfUserHasRoleParams) (bool, error) {
	return m.hasPartnerRole, m.checkRoleError
}

func (m *MockLinkUserToCompanyQueries) AssignRoleToUser(ctx context.Context, arg postgres.AssignRoleToUserParams) error {
	return m.assignRoleError
}

func (m *MockLinkUserToCompanyQueries) RemoveRoleFromUser(ctx context.Context, arg postgres.RemoveRoleFromUserParams) error {
	return m.removeRoleError
}

func (m *MockLinkUserToCompanyQueries) CountCompaniesByOwnerID(ctx context.Context, ownerID sql.NullInt32) (int64, error) {
	return m.companyCount, m.countCompanyError
}

// testLinkUserToCompanyHandler is a test version of CompanyHandler for LinkUserToCompany
type testLinkUserToCompanyHandler struct {
	mockQueries *MockLinkUserToCompanyQueries
}

// LinkUserToCompany is a simplified version for testing
func (h *testLinkUserToCompanyHandler) LinkUserToCompany(w http.ResponseWriter, r *http.Request) {
	companyExternalID := chi.URLParam(r, "externalID")
	if companyExternalID == "" {
		http.Error(w, `{"code":"001","message":"invalid companyExternalID"}`, http.StatusBadRequest)
		return
	}

	var payload LinkUserToCompanyRequest
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, `{"code":"001","message":"invalid request body"}`, http.StatusBadRequest)
		return
	}

	// Get current company and previous owner info
	currentCompany, err := h.mockQueries.GetCompanyByExternalID(r.Context(), companyExternalID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, `{"code":"001","message":"company not found"}`, http.StatusNotFound)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	var previousOwnerID sql.NullInt32
	if currentCompany.OwnerID.Valid {
		previousOwnerID = currentCompany.OwnerID
	}

	// Validate user exists and is active
	userInfo, err := h.mockQueries.GetUserByExternalID(r.Context(), payload.UserExternalID)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(w, `{"code":"001","message":"user not found"}`, http.StatusBadRequest)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	if !userInfo.IsActive {
		http.Error(w, `{"code":"001","message":"user must be active to be linked as company owner"}`, http.StatusBadRequest)
		return
	}

	// Update company owner
	updatedCompany, err := h.mockQueries.UpdateCompanyOwner(r.Context(), postgres.UpdateCompanyOwnerParams{
		ExternalID: companyExternalID,
		OwnerID:    sql.NullInt32{Int32: userInfo.ID, Valid: true},
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Check if user has partner role
	hasPartnerRole, err := h.mockQueries.CheckIfUserHasRole(r.Context(), postgres.CheckIfUserHasRoleParams{
		UserID: userInfo.ID,
		Name:   "partner",
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Assign partner role if user doesn't have it
	if !hasPartnerRole {
		err = h.mockQueries.AssignRoleToUser(r.Context(), postgres.AssignRoleToUserParams{
			UserID: userInfo.ID,
			Name:   "partner",
		})
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}
	}

	// Handle previous owner role removal
	if previousOwnerID.Valid && previousOwnerID.Int32 != userInfo.ID {
		companyCount, err := h.mockQueries.CountCompaniesByOwnerID(r.Context(), previousOwnerID)
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		if companyCount == 0 {
			err = h.mockQueries.RemoveRoleFromUser(r.Context(), postgres.RemoveRoleFromUserParams{
				UserID: previousOwnerID.Int32,
				Name:   "partner",
			})
			if err != nil {
				http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
				return
			}
		}
	}

	response := struct {
		Data LinkUserToCompanyResponse `json:"data"`
	}{
		Data: LinkUserToCompanyResponse{
			CompanyExternalID: updatedCompany.ExternalID,
			CompanyName:       updatedCompany.Name,
			UserExternalID:    payload.UserExternalID,
			UserName:          userInfo.Name,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestLinkUserToCompany(t *testing.T) {
	tests := []struct {
		name                    string
		companyExternalID       string
		requestBody             LinkUserToCompanyRequest
		mockCompany             postgres.GetCompanyByExternalIDRow
		mockUser                postgres.GetUserByExternalIDRow
		mockUpdatedCompany      postgres.UpdateCompanyOwnerRow
		hasPartnerRole          bool
		companyCount            int64
		getCompanyError         error
		getUserError            error
		updateOwnerError        error
		checkRoleError          error
		assignRoleError         error
		removeRoleError         error
		countCompanyError       error
		expectedStatus          int
		expectPartnerAssignment bool
		expectPartnerRemoval    bool
	}{
		{
			name:              "Successfully link user without partner role - should assign partner role",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Valid: false}, // No previous owner
			},
			mockUser: postgres.GetUserByExternalIDRow{
				ID:       2,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdatedCompany: postgres.UpdateCompanyOwnerRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 2, Valid: true},
			},
			hasPartnerRole:          false, // User doesn't have partner role
			expectedStatus:          http.StatusOK,
			expectPartnerAssignment: true,
			expectPartnerRemoval:    false,
		},
		{
			name:              "Successfully link user with existing partner role - no role assignment needed",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Valid: false},
			},
			mockUser: postgres.GetUserByExternalIDRow{
				ID:       2,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdatedCompany: postgres.UpdateCompanyOwnerRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 2, Valid: true},
			},
			hasPartnerRole:          true, // User already has partner role
			expectedStatus:          http.StatusOK,
			expectPartnerAssignment: false,
			expectPartnerRemoval:    false,
		},
		{
			name:              "Replace owner - previous owner has no remaining companies, should remove partner role",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 3, Valid: true}, // Previous owner ID = 3
			},
			mockUser: postgres.GetUserByExternalIDRow{
				ID:       2,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdatedCompany: postgres.UpdateCompanyOwnerRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 2, Valid: true},
			},
			hasPartnerRole:          false,
			companyCount:            0, // Previous owner has no remaining companies
			expectedStatus:          http.StatusOK,
			expectPartnerAssignment: true,
			expectPartnerRemoval:    true,
		},
		{
			name:              "Replace owner - previous owner has remaining companies, keep partner role",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 3, Valid: true}, // Previous owner ID = 3
			},
			mockUser: postgres.GetUserByExternalIDRow{
				ID:       2,
				Name:     "John Doe",
				IsActive: true,
			},
			mockUpdatedCompany: postgres.UpdateCompanyOwnerRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Int32: 2, Valid: true},
			},
			hasPartnerRole:          false,
			companyCount:            2, // Previous owner has 2 remaining companies
			expectedStatus:          http.StatusOK,
			expectPartnerAssignment: true,
			expectPartnerRemoval:    false, // Should NOT remove partner role
		},
		{
			name:              "Company not found",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9999",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			getCompanyError:         sql.ErrNoRows,
			expectedStatus:          http.StatusNotFound,
			expectPartnerAssignment: false,
			expectPartnerRemoval:    false,
		},
		{
			name:              "User not found",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9999",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Valid: false},
			},
			getUserError:            sql.ErrNoRows,
			expectedStatus:          http.StatusBadRequest,
			expectPartnerAssignment: false,
			expectPartnerRemoval:    false,
		},
		{
			name:              "User not active",
			companyExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
			requestBody: LinkUserToCompanyRequest{
				UserExternalID: "01JR0X8RNYFJECMV1NBNVK9922",
			},
			mockCompany: postgres.GetCompanyByExternalIDRow{
				ID:         1,
				Name:       "Test Company",
				ExternalID: "01JR0X8RNYFJECMV1NBNVK9921",
				OwnerID:    sql.NullInt32{Valid: false},
			},
			mockUser: postgres.GetUserByExternalIDRow{
				ID:       2,
				Name:     "John Doe",
				IsActive: false, // User is not active
			},
			expectedStatus:          http.StatusBadRequest,
			expectPartnerAssignment: false,
			expectPartnerRemoval:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockLinkUserToCompanyQueries{
				company:           tt.mockCompany,
				user:              tt.mockUser,
				updatedCompany:    tt.mockUpdatedCompany,
				hasPartnerRole:    tt.hasPartnerRole,
				companyCount:      tt.companyCount,
				getCompanyError:   tt.getCompanyError,
				getUserError:      tt.getUserError,
				updateOwnerError:  tt.updateOwnerError,
				checkRoleError:    tt.checkRoleError,
				assignRoleError:   tt.assignRoleError,
				removeRoleError:   tt.removeRoleError,
				countCompanyError: tt.countCompanyError,
			}

			// Create test handler
			testHandler := &testLinkUserToCompanyHandler{
				mockQueries: mockQueries,
			}

			// Create router and add route
			router := chi.NewRouter()
			router.Put("/{externalID}/owner", testHandler.LinkUserToCompany)

			// Create request body
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PUT", "/"+tt.companyExternalID+"/owner", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Call handler
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					Data LinkUserToCompanyResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.mockUpdatedCompany.Name, response.Data.CompanyName)
				assert.Equal(t, tt.mockUpdatedCompany.ExternalID, response.Data.CompanyExternalID)
				assert.Equal(t, tt.requestBody.UserExternalID, response.Data.UserExternalID)
				assert.Equal(t, tt.mockUser.Name, response.Data.UserName)
			}

			// Note: In a real test, you would verify that the role assignment/removal methods were called
			// This simplified test focuses on the HTTP response behavior
		})
	}
}

// MockGetAllCompaniesQueries is a mock for testing GetAllCompanies
type MockGetAllCompaniesQueries struct {
	companies []postgres.GetAllCompaniesRow
	err       error
}

func (m *MockGetAllCompaniesQueries) GetAllCompanies(ctx context.Context, arg postgres.GetAllCompaniesParams) ([]postgres.GetAllCompaniesRow, error) {
	return m.companies, m.err
}

// testGetAllCompaniesHandler is a test version of CompanyHandler for GetAllCompanies
type testGetAllCompaniesHandler struct {
	mockQueries *MockGetAllCompaniesQueries
}

// GetAllCompanies is a simplified version for testing
func (h *testGetAllCompaniesHandler) GetAllCompanies(w http.ResponseWriter, r *http.Request) {
	companies, err := h.mockQueries.GetAllCompanies(r.Context(), postgres.GetAllCompaniesParams{
		Limit:  10,
		Offset: 0,
	})
	if err != nil {
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Simplified response for testing
	response := struct {
		PageNumber int                          `json:"pageNumber"`
		Limit      int                          `json:"limit"`
		TotalItems int                          `json:"totalItems"`
		TotalPages int                          `json:"totalPages"`
		Data       []GetActiveCompaniesResponse `json:"data"`
	}{
		PageNumber: 1,
		Limit:      10,
		TotalItems: len(companies),
		TotalPages: 1,
		Data:       make([]GetActiveCompaniesResponse, len(companies)),
	}

	for i, company := range companies {
		response.Data[i] = GetActiveCompaniesResponse{
			Name:       company.Name,
			Cnpj:       company.Cnpj,
			Bio:        company.Bio,
			Picture:    company.Picture,
			ExternalID: company.ExternalID,
			IsActive:   company.IsActive,
			Rating:     company.Rating,
			PixKey:     company.PixKey,
			CreatedAt:  company.CreatedAt.String(),
			UpdatedAt:  company.UpdatedAt.String(),
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestGetAllCompanies(t *testing.T) {
	tests := []struct {
		name                 string
		mockCompanies        []postgres.GetAllCompaniesRow
		mockError            error
		expectedStatus       int
		expectedCount        int
		hasActiveAndInactive bool
	}{
		{
			name: "Successfully retrieve all companies (active and inactive)",
			mockCompanies: []postgres.GetAllCompaniesRow{
				{
					ID:         1,
					Name:       "Active Company",
					Cnpj:       "12345678000195",
					Bio:        "Active company bio",
					Picture:    "https://example.com/active.jpg",
					ExternalID: "active-company-123",
					IsActive:   true,
					Rating:     4.5,
					PixKey:     "<EMAIL>",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
				},
				{
					ID:         2,
					Name:       "Inactive Company",
					Cnpj:       "98765432000195",
					Bio:        "Inactive company bio",
					Picture:    "https://example.com/inactive.jpg",
					ExternalID: "inactive-company-456",
					IsActive:   false,
					Rating:     3.0,
					PixKey:     "<EMAIL>",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
				},
			},
			mockError:            nil,
			expectedStatus:       http.StatusOK,
			expectedCount:        2,
			hasActiveAndInactive: true,
		},
		{
			name:                 "No companies found",
			mockCompanies:        []postgres.GetAllCompaniesRow{},
			mockError:            nil,
			expectedStatus:       http.StatusOK,
			expectedCount:        0,
			hasActiveAndInactive: false,
		},
		{
			name:                 "Database error",
			mockCompanies:        nil,
			mockError:            sql.ErrConnDone,
			expectedStatus:       http.StatusInternalServerError,
			expectedCount:        0,
			hasActiveAndInactive: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockGetAllCompaniesQueries{
				companies: tt.mockCompanies,
				err:       tt.mockError,
			}

			// Create test handler
			testHandler := &testGetAllCompaniesHandler{
				mockQueries: mockQueries,
			}

			// Create request
			req := httptest.NewRequest("GET", "/v1/company/all", nil)
			w := httptest.NewRecorder()

			// Call handler
			testHandler.GetAllCompanies(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					PageNumber int                          `json:"pageNumber"`
					Limit      int                          `json:"limit"`
					TotalItems int                          `json:"totalItems"`
					TotalPages int                          `json:"totalPages"`
					Data       []GetActiveCompaniesResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.expectedCount, len(response.Data))
				assert.Equal(t, tt.expectedCount, response.TotalItems)

				// Check that both active and inactive companies are returned
				if tt.hasActiveAndInactive {
					activeFound := false
					inactiveFound := false
					for _, company := range response.Data {
						if company.IsActive {
							activeFound = true
						} else {
							inactiveFound = true
						}
					}
					assert.True(t, activeFound, "Should find at least one active company")
					assert.True(t, inactiveFound, "Should find at least one inactive company")
				}
			}
		})
	}
}

// MockGetActiveCompaniesWithLocationQueries is a mock for testing GetActiveCompanies with location filtering
type MockGetActiveCompaniesWithLocationQueries struct {
	companies         []postgres.GetActiveCompaniesRow
	companiesLocation []postgres.GetActiveCompaniesWithLocationRow
	err               error
}

func (m *MockGetActiveCompaniesWithLocationQueries) GetActiveCompanies(ctx context.Context, arg postgres.GetActiveCompaniesParams) ([]postgres.GetActiveCompaniesRow, error) {
	return m.companies, m.err
}

func (m *MockGetActiveCompaniesWithLocationQueries) GetActiveCompaniesWithLocation(ctx context.Context, arg postgres.GetActiveCompaniesWithLocationParams) ([]postgres.GetActiveCompaniesWithLocationRow, error) {
	return m.companiesLocation, m.err
}

// testGetActiveCompaniesHandler is a test version of CompanyHandler for GetActiveCompanies with location filtering
type testGetActiveCompaniesHandler struct {
	mockQueries *MockGetActiveCompaniesWithLocationQueries
}

// GetActiveCompanies is a simplified version for testing location-based filtering
func (h *testGetActiveCompaniesHandler) GetActiveCompanies(w http.ResponseWriter, r *http.Request) {
	latStr := r.URL.Query().Get("lat")
	longStr := r.URL.Query().Get("long")

	// Check if location parameters are provided
	hasLocationParams := latStr != "" && longStr != ""

	// If only one coordinate is provided, return empty array (fast fail)
	if (latStr != "" && longStr == "") || (latStr == "" && longStr != "") {
		response := struct {
			PageNumber int                          `json:"pageNumber"`
			Limit      int                          `json:"limit"`
			TotalItems int                          `json:"totalItems"`
			TotalPages int                          `json:"totalPages"`
			Data       []GetActiveCompaniesResponse `json:"data"`
		}{
			PageNumber: 1,
			Limit:      10,
			TotalItems: 0,
			TotalPages: 0,
			Data:       []GetActiveCompaniesResponse{},
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	var lat, long float64
	var err error

	// Parse and validate coordinates if provided
	if hasLocationParams {
		lat, err = strconv.ParseFloat(latStr, 64)
		if err != nil {
			response := struct {
				PageNumber int                          `json:"pageNumber"`
				Limit      int                          `json:"limit"`
				TotalItems int                          `json:"totalItems"`
				TotalPages int                          `json:"totalPages"`
				Data       []GetActiveCompaniesResponse `json:"data"`
			}{
				PageNumber: 1,
				Limit:      10,
				TotalItems: 0,
				TotalPages: 0,
				Data:       []GetActiveCompaniesResponse{},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
			return
		}

		long, err = strconv.ParseFloat(longStr, 64)
		if err != nil {
			response := struct {
				PageNumber int                          `json:"pageNumber"`
				Limit      int                          `json:"limit"`
				TotalItems int                          `json:"totalItems"`
				TotalPages int                          `json:"totalPages"`
				Data       []GetActiveCompaniesResponse `json:"data"`
			}{
				PageNumber: 1,
				Limit:      10,
				TotalItems: 0,
				TotalPages: 0,
				Data:       []GetActiveCompaniesResponse{},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
			return
		}

		// Validate coordinate ranges
		if lat < -90 || lat > 90 || long < -180 || long > 180 {
			response := struct {
				PageNumber int                          `json:"pageNumber"`
				Limit      int                          `json:"limit"`
				TotalItems int                          `json:"totalItems"`
				TotalPages int                          `json:"totalPages"`
				Data       []GetActiveCompaniesResponse `json:"data"`
			}{
				PageNumber: 1,
				Limit:      10,
				TotalItems: 0,
				TotalPages: 0,
				Data:       []GetActiveCompaniesResponse{},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
			return
		}
	}

	var totalItems int
	var responseData []GetActiveCompaniesResponse

	if hasLocationParams {
		// Use location-based query
		companiesWithLocation, err := h.mockQueries.GetActiveCompaniesWithLocation(r.Context(), postgres.GetActiveCompaniesWithLocationParams{
			Limit:   10,
			Offset:  0,
			Column3: long,  // longitude first for PostGIS
			Column4: lat,   // latitude second for PostGIS
			Column5: 10000, // 10km in meters
		})
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		responseData = make([]GetActiveCompaniesResponse, len(companiesWithLocation))
		for i, company := range companiesWithLocation {
			totalItems = int(company.TotalCount)
			responseData[i] = GetActiveCompaniesResponse{
				Name:       company.Name,
				Cnpj:       company.Cnpj,
				Bio:        company.Bio,
				Picture:    company.Picture,
				ExternalID: company.ExternalID,
				IsActive:   company.IsActive,
				Rating:     company.Rating,
				PixKey:     company.PixKey,
				CreatedAt:  company.CreatedAt.String(),
				UpdatedAt:  company.UpdatedAt.String(),
			}
		}
	} else {
		// Use regular query without location filtering
		companies, err := h.mockQueries.GetActiveCompanies(r.Context(), postgres.GetActiveCompaniesParams{
			Offset: 0,
			Limit:  10,
		})
		if err != nil {
			http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
			return
		}

		responseData = make([]GetActiveCompaniesResponse, len(companies))
		for i, company := range companies {
			totalItems = int(company.TotalCount)
			responseData[i] = GetActiveCompaniesResponse{
				Name:       company.Name,
				Cnpj:       company.Cnpj,
				Bio:        company.Bio,
				Picture:    company.Picture,
				ExternalID: company.ExternalID,
				IsActive:   company.IsActive,
				Rating:     company.Rating,
				PixKey:     company.PixKey,
				CreatedAt:  company.CreatedAt.String(),
				UpdatedAt:  company.UpdatedAt.String(),
			}
		}
	}

	response := struct {
		PageNumber int                          `json:"pageNumber"`
		Limit      int                          `json:"limit"`
		TotalItems int                          `json:"totalItems"`
		TotalPages int                          `json:"totalPages"`
		Data       []GetActiveCompaniesResponse `json:"data"`
	}{
		PageNumber: 1,
		Limit:      10,
		TotalItems: totalItems,
		TotalPages: 1,
		Data:       responseData,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestGetActiveCompaniesWithLocationFiltering(t *testing.T) {
	tests := []struct {
		name                  string
		queryParams           map[string]string
		mockCompanies         []postgres.GetActiveCompaniesRow
		mockCompaniesLocation []postgres.GetActiveCompaniesWithLocationRow
		mockError             error
		expectedStatus        int
		expectedCount         int
		expectLocationQuery   bool
	}{
		{
			name:        "No location parameters - use regular query",
			queryParams: map[string]string{},
			mockCompanies: []postgres.GetActiveCompaniesRow{
				{
					ID:         1,
					Name:       "Company 1",
					Cnpj:       "12345678000195",
					Bio:        "Company 1 bio",
					Picture:    "https://example.com/1.jpg",
					ExternalID: "company-1",
					IsActive:   true,
					Rating:     4.5,
					PixKey:     "<EMAIL>",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					TotalCount: 1,
				},
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       1,
			expectLocationQuery: false,
		},
		{
			name: "Valid coordinates - use location-based query",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "-35.2110",
			},
			mockCompaniesLocation: []postgres.GetActiveCompaniesWithLocationRow{
				{
					ID:         1,
					Name:       "Nearby Company",
					Cnpj:       "12345678000195",
					Bio:        "Nearby company bio",
					Picture:    "https://example.com/nearby.jpg",
					ExternalID: "nearby-company",
					IsActive:   true,
					Rating:     4.8,
					PixKey:     "<EMAIL>",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					TotalCount: 1,
				},
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       1,
			expectLocationQuery: true,
		},
		{
			name: "Only latitude provided - return empty array",
			queryParams: map[string]string{
				"lat": "-5.7945",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Only longitude provided - return empty array",
			queryParams: map[string]string{
				"long": "-35.2110",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Invalid latitude - return empty array",
			queryParams: map[string]string{
				"lat":  "invalid",
				"long": "-35.2110",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Invalid longitude - return empty array",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "invalid",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Latitude out of range - return empty array",
			queryParams: map[string]string{
				"lat":  "95.0",
				"long": "-35.2110",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Longitude out of range - return empty array",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "185.0",
			},
			mockError:           nil,
			expectedStatus:      http.StatusOK,
			expectedCount:       0,
			expectLocationQuery: false,
		},
		{
			name: "Valid coordinates but no companies within range",
			queryParams: map[string]string{
				"lat":  "-5.7945",
				"long": "-35.2110",
			},
			mockCompaniesLocation: []postgres.GetActiveCompaniesWithLocationRow{},
			mockError:             nil,
			expectedStatus:        http.StatusOK,
			expectedCount:         0,
			expectLocationQuery:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockGetActiveCompaniesWithLocationQueries{
				companies:         tt.mockCompanies,
				companiesLocation: tt.mockCompaniesLocation,
				err:               tt.mockError,
			}

			// Create test handler
			testHandler := &testGetActiveCompaniesHandler{
				mockQueries: mockQueries,
			}

			// Build URL with query parameters
			url := "/v1/company"
			if len(tt.queryParams) > 0 {
				url += "?"
				params := make([]string, 0, len(tt.queryParams))
				for key, value := range tt.queryParams {
					params = append(params, key+"="+value)
				}
				url += strings.Join(params, "&")
			}

			// Create request
			req := httptest.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// Call handler
			testHandler.GetActiveCompanies(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response struct {
					PageNumber int                          `json:"pageNumber"`
					Limit      int                          `json:"limit"`
					TotalItems int                          `json:"totalItems"`
					TotalPages int                          `json:"totalPages"`
					Data       []GetActiveCompaniesResponse `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, tt.expectedCount, len(response.Data))
				assert.Equal(t, tt.expectedCount, response.TotalItems)

				// Verify correct query was used based on expected behavior
				if tt.expectLocationQuery && tt.expectedCount > 0 {
					// Should have used location-based query and returned companies
					assert.Greater(t, len(response.Data), 0)
				}
			}
		})
	}
}

// Note: Balance and withdrawal endpoint tests have been removed as they now use
// the payout-based system instead of Woovi sub-account integration.
// New tests should be written to test the payout-based functionality.

// TODO: Add new tests for payout-based balance and withdrawal endpoints
// The old Woovi sub-account based tests have been removed.

// MockGetUserCompanyBalanceQueries is a mock for testing GetBalance (single company balance)
type MockGetUserCompanyBalanceQueries struct {
	balance      postgres.GetUserCompanyBalanceRow
	adminBalance postgres.GetCompanyBalanceByExternalIDRow
	err          error
	adminErr     error
}

func (m *MockGetUserCompanyBalanceQueries) GetUserCompanyBalance(ctx context.Context, arg postgres.GetUserCompanyBalanceParams) (postgres.GetUserCompanyBalanceRow, error) {
	return m.balance, m.err
}

func (m *MockGetUserCompanyBalanceQueries) GetCompanyBalanceByExternalID(ctx context.Context, externalID string) (postgres.GetCompanyBalanceByExternalIDRow, error) {
	return m.adminBalance, m.adminErr
}

// testGetBalanceHandler is a test version of CompanyHandler for GetBalance
type testGetBalanceHandler struct {
	mockQueries *MockGetUserCompanyBalanceQueries
}

// GetBalance is a simplified version for testing
func (h *testGetBalanceHandler) GetBalance(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		http.Error(w, `{"code":"001","message":"user not authenticated"}`, http.StatusUnauthorized)
		return
	}

	// Get external_id from URL path (simulated)
	externalID := "test-company-id" // For testing purposes

	// Check if user has admin role for unrestricted access
	isAdmin := slices.Contains(user.Roles, "admin")

	var balance postgres.GetUserCompanyBalanceRow
	var err error

	if isAdmin {
		// Admin users can access any company's balance without owner restriction
		adminBalance, adminErr := h.mockQueries.GetCompanyBalanceByExternalID(ctx, externalID)
		if adminErr != nil {
			err = adminErr
		} else {
			// Convert to the same structure as GetUserCompanyBalance for consistent response
			balance = postgres.GetUserCompanyBalanceRow(adminBalance)
		}
	} else {
		// Non-admin users can only access companies they own
		balance, err = h.mockQueries.GetUserCompanyBalance(r.Context(), postgres.GetUserCompanyBalanceParams{
			OwnerID:    sql.NullInt32{Int32: user.ID, Valid: true},
			ExternalID: externalID,
		})
	}
	if err != nil {
		if err.Error() == "no rows in result set" {
			http.Error(w, `{"code":"001","message":"company not found"}`, http.StatusNotFound)
			return
		}
		http.Error(w, `{"code":"001","message":"internal server error"}`, http.StatusInternalServerError)
		return
	}

	// Handle nullable LatestAvailableDate
	var latestAvailableDate string
	if balance.LatestAvailableDate != nil {
		if dateTime, ok := balance.LatestAvailableDate.(time.Time); ok {
			latestAvailableDate = dateTime.Format(time.RFC3339)
		}
	}

	// Convert balance amount to int32
	var availableBalance int32
	if balance.AvailableBalance != nil {
		if balanceInt, ok := balance.AvailableBalance.(int64); ok {
			availableBalance = int32(balanceInt)
		}
	}

	response := GetBalanceResponse{
		ExternalID:           balance.ExternalID,
		Name:                 balance.Name,
		PixKey:               balance.PixKey,
		IsActive:             balance.IsActive,
		Balance:              availableBalance,
		AvailablePayoutCount: balance.AvailablePayoutCount,
		LatestAvailableDate:  latestAvailableDate,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func TestGetBalance(t *testing.T) {
	tests := []struct {
		name             string
		userID           int32
		userExternalID   string
		userRoles        []string
		queryParams      map[string]string
		mockBalance      postgres.GetUserCompanyBalanceRow
		mockAdminBalance postgres.GetCompanyBalanceByExternalIDRow
		mockError        error
		mockAdminError   error
		expectedStatus   int
	}{
		{
			name:           "Successfully retrieve user company balance (partner)",
			userID:         1,
			userExternalID: "user-123",
			userRoles:      []string{"user", "partner"},
			queryParams:    map[string]string{},
			mockBalance: postgres.GetUserCompanyBalanceRow{
				ID:                   1,
				ExternalID:           "01JR0X8RNYFJECMV1NBNVK9921",
				Name:                 "Company A",
				PixKey:               "<EMAIL>",
				IsActive:             true,
				AvailableBalance:     int64(15000), // R$150.00
				AvailablePayoutCount: 3,
				LatestAvailableDate:  time.Now(),
				TotalCount:           1,
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Successfully retrieve any company balance (admin)",
			userID:         2,
			userExternalID: "admin-456",
			userRoles:      []string{"user", "admin"},
			queryParams:    map[string]string{},
			mockAdminBalance: postgres.GetCompanyBalanceByExternalIDRow{
				ID:                   2,
				ExternalID:           "01JR0X8RNYFJECMV1NBNVK9922",
				Name:                 "Company B",
				PixKey:               "<EMAIL>",
				IsActive:             true,
				AvailableBalance:     int64(25000), // R$250.00
				AvailablePayoutCount: 5,
				LatestAvailableDate:  time.Now(),
				TotalCount:           1,
			},
			mockAdminError: nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Company not found (partner)",
			userID:         3,
			userExternalID: "user-789",
			userRoles:      []string{"user", "partner"},
			queryParams:    map[string]string{},
			mockBalance:    postgres.GetUserCompanyBalanceRow{},
			mockError:      fmt.Errorf("no rows in result set"),
			expectedStatus: http.StatusNotFound,
		},
		{
			name:             "Company not found (admin)",
			userID:           4,
			userExternalID:   "admin-101",
			userRoles:        []string{"user", "admin"},
			queryParams:      map[string]string{},
			mockAdminBalance: postgres.GetCompanyBalanceByExternalIDRow{},
			mockAdminError:   fmt.Errorf("no rows in result set"),
			expectedStatus:   http.StatusNotFound,
		},
		{
			name:           "Database error (partner)",
			userID:         5,
			userExternalID: "user-202",
			userRoles:      []string{"user", "partner"},
			queryParams:    map[string]string{},
			mockBalance:    postgres.GetUserCompanyBalanceRow{},
			mockError:      fmt.Errorf("database connection error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock queries
			mockQueries := &MockGetUserCompanyBalanceQueries{
				balance:      tt.mockBalance,
				adminBalance: tt.mockAdminBalance,
				err:          tt.mockError,
				adminErr:     tt.mockAdminError,
			}

			// Create test handler
			testHandler := &testGetBalanceHandler{
				mockQueries: mockQueries,
			}

			// Build URL with query parameters
			url := "/v1/company/balance"
			if len(tt.queryParams) > 0 {
				url += "?"
				params := []string{}
				for key, value := range tt.queryParams {
					params = append(params, key+"="+value)
				}
				url += strings.Join(params, "&")
			}

			// Create request with user context
			req := httptest.NewRequest("GET", url, nil)

			// Add user to context
			user := middlewares.User{
				ID:         tt.userID,
				ExternalID: tt.userExternalID,
				Roles:      tt.userRoles,
			}
			ctx := context.WithValue(req.Context(), middlewares.UserContextKey, user)
			req = req.WithContext(ctx)

			w := httptest.NewRecorder()

			// Call handler
			testHandler.GetBalance(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				// Parse response
				var response GetBalanceResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Check if user is admin to determine which mock data to use
				isAdmin := slices.Contains(tt.userRoles, "admin")

				if isAdmin {
					// Check response data matches admin mock
					assert.Equal(t, tt.mockAdminBalance.ExternalID, response.ExternalID)
					assert.Equal(t, tt.mockAdminBalance.Name, response.Name)
					assert.Equal(t, tt.mockAdminBalance.PixKey, response.PixKey)
					assert.Equal(t, tt.mockAdminBalance.IsActive, response.IsActive)
					assert.Equal(t, tt.mockAdminBalance.AvailablePayoutCount, response.AvailablePayoutCount)

					// Check balance conversion for admin
					if tt.mockAdminBalance.AvailableBalance != nil {
						if balanceInt, ok := tt.mockAdminBalance.AvailableBalance.(int64); ok {
							assert.Equal(t, int32(balanceInt), response.Balance)
						}
					}
				} else {
					// Check response data matches partner mock
					assert.Equal(t, tt.mockBalance.ExternalID, response.ExternalID)
					assert.Equal(t, tt.mockBalance.Name, response.Name)
					assert.Equal(t, tt.mockBalance.PixKey, response.PixKey)
					assert.Equal(t, tt.mockBalance.IsActive, response.IsActive)
					assert.Equal(t, tt.mockBalance.AvailablePayoutCount, response.AvailablePayoutCount)

					// Check balance conversion for partner
					if tt.mockBalance.AvailableBalance != nil {
						if balanceInt, ok := tt.mockBalance.AvailableBalance.(int64); ok {
							assert.Equal(t, int32(balanceInt), response.Balance)
						}
					}
				}
			}
		})
	}
}
